---

# Git settings.
setup_git_repo: false
git_branch: dev


# Database settings.
db_user: "{{ application_name }}_db_admin"
db_name: "{{ application_name }}"
db_password: XAAE*zsjJrF!RtKzX!x87Rdg
db_host: 127.0.0.1
db_port: 5432


# Gunicorn settings. For the number of workers, a good rule to follow is
# 2 x number of CPUs + 1
gunicorn_num_workers: 3

# Setting this to 1 will restart the Gunicorn process each time
# you make a request, basically reloading the code. Very handy
# when developing. Set to 0 for unlimited requests (default).
gunicorn_max_requests: 1


# RabbitMQ settings.
rabbitmq_server_name: "{{ inventory_hostname }}"

rabbitmq_admin_user: admin
rabbitmq_admin_password: password

rabbitmq_application_vhost: "{{ application_name }}"
rabbitmq_application_user: "{{ application_name }}"
rabbitmq_application_password: password


# Celery settings.
celery_num_workers: 2


# Application settings.
django_settings_file: "{{ application_name }}.settings.development"
django_secret_key: "akr2icmg1n8%z^3fe3c+)5d0(t^cy-2_25rrl35a7@!scna^1#"

broker_url: "amqp://{{ rabbitmq_application_user }}:{{ rabbitmq_application_password }}@localhost/{{ rabbitmq_application_vhost }}"

run_django_db_migrations: true
run_django_collectstatic: true


# Nginx settings.
ssl_crt: |
  -----BEGIN CERTIFICATE-----
  MIIDOjCCAiKgAwIBAgIBADANBgkqhkiG9w0BAQsFADAaMRgwFgYDVQQDDA9kZXYu
  ZXhhbXBsZS5jb20wIhgPMTk3MDAxMDEwMDAwMDBaGA85OTk5MTIzMTIzNTk1OVow
  GjEYMBYGA1UEAwwPZGV2LmV4YW1wbGUuY29tMIIBIjANBgkqhkiG9w0BAQEFAAOC
  AQ8AMIIBCgKCAQEAuTyvpgw5iC7vHGr9cpCa1yVW3rTc/81PWqMPL3lKmV4IzHd7
  +50QKOFCQE6nfhYS+jVM/3dk8DQgaaTdo1BVF9kT/p1SQE2aE4AFHfPKXP1M+MFB
  oqK6uujejns4sItZg5yj6QTyTBNOHkaXeCObYpAnp+HokqT5Nmrr/uzPZc7jNZ41
  ehM2mL3nJ7Zgl40nQj4UqFLsXmlG4g6DKEKilRIpJClLdRm6lydgdVi2HApE4adk
  DOcIXXY687Y/LFoPFrgpztWv/O0/oJguV7LHttjS9b6LzjF8k7rWqCT4muHy1fYc
  JXLIOaUrWEru9FQegppwIo6tQjoZuy09lLOztQIDAQABo4GGMIGDMAkGA1UdEwQC
  MAAwHQYDVR0OBBYEFG2JDPGRCvuRmxMNQcewxwamyehtMB8GA1UdIwQYMBaAFG2J
  DPGRCvuRmxMNQcewxwamyehtMBoGA1UdEQQTMBGCD2Rldi5leGFtcGxlLmNvbTAa
  BgNVHRIEEzARgg9kZXYuZXhhbXBsZS5jb20wDQYJKoZIhvcNAQELBQADggEBAIe2
  obG3657lBKtQEvRVnhJC8utlNIAo0U3Ys6+jmU87LEijVMVwhZreleLC2jI4DeLe
  JDf2uenifKQbhMCoWBJvoPxP5QjwpOaKvxuI/+xhSG4pQfBV9kb6mHsYhykY0sX1
  //JgCoumWwLbLQnB2tXV32Dqm+HUWWXqLS/aenNx0HWJwfFCLHPTTPYRn9ESy+oh
  7frGtzjIFEx/OV2yQwBXmMxQjhUa82/Od99vEmiLgC4LLqXVtadNnumENMCRbw1P
  99Z7EbZ9F206VVc8aSCLNhphPAct0wFYTQ59tFGFj637SsrI6LhP/wKXOJS5WLSG
  sK13YvMF3uL9YD7JNGI=
  -----END CERTIFICATE-----

# This key is for demo purposes only. If you're committing your keys
# to your git repo, you'd probably want it encrypted. You can use
# ansible-vault to do this.
ssl_key: |
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

apple_apps:
  - team_id: "UAT6W6PV8T"  # Replace with your first app's Team ID
    bundle_id: "com.bigbaboon.rewards.dev"  # Replace with your first app's bundle ID
  - team_id: "UAT6W6PV8T"  # Replace with your second app's Team ID
    bundle_id: "com.bigbaboon.rewardsclerk.dev"  # Replace with your second app's bundle ID