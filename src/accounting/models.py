from django.db import models
from django.conf import settings
import uuid


class SubscriptionPlan(models.Model):
    """The plan a user subscribes to each month."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=65535)
    price = models.DecimalField(decimal_places=2, max_digits=5)
    max_users = models.PositiveIntegerField()
    is_active = models.BooleanField(default=False)

    def __unicode__(self):
        return u'%s - %s - %s max users' % (self.name, self.price, self.max_users)

    @property
    def price_per_user(self):
        if self.price == 0:
            return 0

        return self.price / self.max_users


def get_lowest_subscription_plan():
    return SubscriptionPlan.objects.all().order_by('max_users').first()

class Subscription(models.Model):
    """The model which holds details on a user's subscription."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(settings.AUTH_USER_MODEL, related_name='subscription', on_delete=models.PROTECT)
    plan = models.ForeignKey(SubscriptionPlan, default=get_lowest_subscription_plan, on_delete=models.PROTECT)
    expires_at = models.DateTimeField(null=True, blank=True)


class SubscriptionPlanPurchase(models.Model):
    """A model that records when a subscription plan was purchased. (i.e. every time a subscription is charged)"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    plan = models.ForeignKey(SubscriptionPlan, blank=False, related_name='purchase', on_delete=models.PROTECT)
    created_at = models.DateTimeField(auto_now_add=True)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.PROTECT)
