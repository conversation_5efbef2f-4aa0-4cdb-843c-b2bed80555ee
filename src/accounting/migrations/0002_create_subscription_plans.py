# Generated by Django 3.2 on 2023-02-04 12:18

from django.db import migrations


def create_subscription_plans(apps, schema_editor):
    SubscriptionPlan = apps.get_model('accounting', 'SubscriptionPlan')
    SubscriptionPlan(name='Free', price=0.0, max_users=50).save()
    SubscriptionPlan(name='Basic', price=5.0, max_users=200).save()
    SubscriptionPlan(name='Plus', price=20.0, max_users=1000).save()
    SubscriptionPlan(name='Pro', price=75.0, max_users=5000).save()
    SubscriptionPlan(name='Ultimate', price=150.0, max_users=15000).save()


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounting', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(
            code=create_subscription_plans,
        ),
    ]
