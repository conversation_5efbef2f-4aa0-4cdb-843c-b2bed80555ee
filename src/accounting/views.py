from rest_framework import generics, permissions, filters, status
from rest_framework.response import Response

from django.shortcuts import get_object_or_404

from .models import Subscription, SubscriptionPlan, SubscriptionPlanPurchase
from .serializers import SubscriptionPlanSerializer, SubscriptionSerializer


class ListSubscriptionPlanView(generics.ListAPIView):
    serializer_class = SubscriptionPlanSerializer
    queryset = SubscriptionPlan.objects.all()


class CreateSubscriptionView(generics.CreateAPIView):
    serializer_class = SubscriptionSerializer
    queryset = Subscription.objects.all()

    def create(self, request, *args, **kwargs):
        data = request.data
        data['user'] = request.user.pk
        serializer = SubscriptionSerializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)


class RetrieveUpdateSubscriptionView(generics.RetrieveUpdateAPIView):
    serializer_class = SubscriptionSerializer
    queryset = Subscription.objects.all()
    
    def get_object(self):
        if self.kwargs.get('pk', None) == 'me':
            return get_object_or_404(Subscription.objects.filter(user=self.request.user))

        return super(RetrieveUpdateSubscriptionView, self).get_object()