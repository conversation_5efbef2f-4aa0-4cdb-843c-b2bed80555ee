from .models import User, PrivacyPolicy, PrivacyPolicyVersion, PrivacyPolicyAcceptance
from rest_framework import serializers
from django.db import IntegrityError

class UserSerializer(serializers.ModelSerializer):
    auth_token = serializers.StringRelatedField(required=False)

    class Meta:
        model = User
        fields = ['id', 'first_name', 'last_name', 'email', 'password', 'auth_token', 'is_clerk']
        read_only_fields = ['id', 'auth_token']
        extra_kwargs = {'password': {'write_only': True}}

    def create(self, validated_data):
        email = validated_data.pop('email')
        password = validated_data.pop('password')
        user = User.objects.create_user(
            email=email,
            password=password,
            **validated_data)

        return user
    

class VerifyUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id']
        read_only_fields = ['id']
        

class PrivacyPolicyVersionSerializer(serializers.ModelSerializer):
    policy_type = serializers.SerializerMethodField()
    class Meta:
        model = PrivacyPolicyVersion
        fields = ['id', 'content', 'version', 'policy_type', 'created_at']

    def get_policy_type(self, obj):
        return obj.policy.type


class PrivacyPolicySerializer(serializers.ModelSerializer):
    latest_version = PrivacyPolicyVersionSerializer()
    class Meta:
        model = PrivacyPolicy
        fields = ['id', 'type', 'created_at', 'latest_version']


class PrivacyPolicyAcceptanceSerializer(serializers.ModelSerializer):
    user = serializers.PrimaryKeyRelatedField(queryset=User.objects.all())
    version = serializers.PrimaryKeyRelatedField(queryset=PrivacyPolicyVersion.objects.all())
    class Meta:
        model = PrivacyPolicyAcceptance
        fields = ['id', 'version', 'accepted', 'user', 'created_at']